//
//  RNBridge.swift
//  RNModule
//
//  Created by <PERSON>  on 2025/7/9.
//

import UIKit
import ObjectiveC
// 不暴露 React 和 CodePush 给外部，保持完全封装
@_implementationOnly import React
@_implementationOnly import React_RCTAppDelegate
@_implementationOnly import ReactAppDependencyProvider
// CodePush 是 Objective-C 库, 通过 CodePushWrapper.h 访问

// MARK: - 私有日志函数（避免与 React 模块的 print 方法冲突）
private func logMessage(_ message: String) {
    // 使用全局作用域明确调用 Swift 的 print 函数
    Swift.print("[RNBridge] \(message)")
}

// MARK: - RNModuleBridgeDelegate
///
/// 实现了 RCTDefaultReactNativeFactoryDelegate 协议，为 RNModule 提供定制化的 Bundle URL
///
private class RNModuleBridgeDelegate: RCTDefaultReactNativeFactoryDelegate {
    private let getBundleURL: () -> URL?

    init(getBundleURL: @escaping () -> URL?) {
        self.getBundleURL = getBundleURL
        super.init()
    }

    override func sourceURL(for bridge: RCTBridge) -> URL? {
        self.bundleURL()
    }

    override func bundleURL() -> URL? {
        return getBundleURL()
    }
}

// MARK: - 统一的 RNBridge 类
@objc(RNBridge)
public class RNBridge: NSObject {

    // MARK: - 静态配置管理
    private static let _initLock = NSLock()
    
    // MARK: - 实例属性
    let enableCodePush: Bool
    
    // MARK: - 初始化方法
    @objc public init(enableCodePush: Bool = false) {
        self.enableCodePush = enableCodePush
        super.init()
    }
    
    // MARK: - React Native Factory 创建方法
    private static func createReactNativeFactory(enableCodePush: Bool) -> RCTReactNativeFactory {
        logMessage("🏭 创建新的 React Native Factory...")
        
        // 1. 创建 Delegate
        let delegate = RNModuleBridgeDelegate { 
            let bridge = RNBridge(enableCodePush: enableCodePush)
            return bridge.getBundleURLWithPriority()
        }
        delegate.dependencyProvider = RCTAppDependencyProvider()
        
        // 2. 创建 Factory
        let factory = RCTReactNativeFactory(delegate: delegate)
        
        logMessage("✅ React Native Factory 创建完成")
        return factory
    }

    // MARK: - 公开配置信息
    
    /// 是否为Debug版本
    @objc public static var isDebugBuild: Bool {
        #if DEBUG
        return true
        #else
        return false
        #endif
    }
    
    /// 构建类型字符串
    @objc public static var buildType: String {
        return isDebugBuild ? "Debug" : "Release"
    }
    
    /// RNModule框架版本
    @objc public static let moduleVersion: String = "1.1.31"
    
    /// 当前Metro服务器地址（如果有的话）
    @objc public static var currentMetroServerAddress: String? {
        return _currentMetroIP
    }
    
    /// React Native Bundle信息
    @objc public static var bundleInfo: [String: Any] {
        return [
            "isDebug": isDebugBuild,
            "buildType": buildType,
            "moduleVersion": moduleVersion,
            "metroServer": currentMetroServerAddress ?? "not specified"
        ]
    }
    

    
    // MARK: - 私有变量
    private static var _currentMetroIP: String?

    // MARK: - 内部辅助方法（Bundle URL 优先级处理）
    
    /// 检查 Metro 服务器是否可达
    private func isMetroServerReachable(ip: String, timeout: TimeInterval = 3.0) -> Bool {
        let semaphore = DispatchSemaphore(value: 0)
        var isReachable = false
        
        // 构建 Metro 状态检查 URL（使用轻量级的状态端点）
        guard let statusURL = URL(string: "http://\(ip):8081/status") else {
            logMessage("❌ 无效的 Metro 状态检查 URL: \(ip):8081")
            return false
        }
        
        logMessage("🔍 检查 Metro 服务器可达性: \(ip):8081 (超时: \(timeout)s)")
        
        var request = URLRequest(url: statusURL)
        request.httpMethod = "GET"
        request.timeoutInterval = timeout
        request.cachePolicy = .reloadIgnoringLocalCacheData
        
        let task = URLSession.shared.dataTask(with: request) { data, response, error in
            defer { semaphore.signal() }
            
            if let error = error {
                logMessage("❌ Metro 连接失败: \(error.localizedDescription)")
                return
            }
            
            if let httpResponse = response as? HTTPURLResponse {
                logMessage("🔍 Metro 响应状态码: \(httpResponse.statusCode)")
                // Metro 服务器正常运行时通常返回 200 或者一些其他有效状态码
                isReachable = (200...299).contains(httpResponse.statusCode) || httpResponse.statusCode == 404
            }
        }
        
        task.resume()
        
        // 等待响应或超时
        _ = semaphore.wait(timeout: .now() + timeout)
        
        if isReachable {
            logMessage("✅ Metro 服务器可达")
        } else {
            logMessage("❌ Metro 服务器不可达")
        }
        
        return isReachable
    }
    
    private func getBundleURLWithPriority() -> URL? {
        logMessage("🎯 开始按优先级获取 Bundle URL (Mode: \(Self.buildType))")
        
        #if DEBUG
        // DEBUG 模式：首先检查 Metro 服务器，然后使用版本比较
        if let metroURL = tryGetMetroURL() {
            return metroURL
        }
        
        // Metro 不可用，使用版本比较
        logMessage("🔄 DEBUG模式: Metro 不可用，使用版本比较选择最佳 Bundle")
        return selectBundleByVersionComparison(mode: "DEBUG")
        
        #else
        // RELEASE 模式：直接使用版本比较
        logMessage("🔄 Release模式: 基于版本比较选择最佳 Bundle")
        return selectBundleByVersionComparison(mode: "RELEASE")
        #endif
    }
    
    /// 尝试获取 Metro 服务器 URL（仅 DEBUG 模式）
    private func tryGetMetroURL() -> URL? {
        guard let metroIP = Self._currentMetroIP else {
            logMessage("ℹ️ DEBUG模式: 未指定 Metro IP，跳过 Metro 检查")
            return nil
        }
        
        logMessage("🔄 DEBUG模式步骤1: 检查 Metro 服务器 \(metroIP)")
        
        if isMetroServerReachable(ip: metroIP) {
            if let metroURL = URL(string: "http://\(metroIP):8081/index.bundle?platform=ios&dev=true") {
                logMessage("✅ DEBUG模式成功: 使用 Metro URL: \(metroURL)")
                return metroURL
            } else {
                logMessage("❌ Metro URL 构建失败")
            }
        } else {
            logMessage("⚠️ Metro 服务器不可达，开始版本比较回退...")
        }
        
        return nil
    }
    
    /// 基于版本比较选择最佳 Bundle
    private func selectBundleByVersionComparison(mode: String) -> URL? {
        logMessage("🔍 开始版本比较选择 - CodePush 启用状态: \(enableCodePush)")
        
        // 如果 CodePush 被禁用，直接使用内置 Bundle
        if !enableCodePush {
            logMessage("🚫 CodePush 已禁用，直接使用内置 Bundle")
            if let bundledURL = Bundle.rnModule?.mainJSBundleURL {
                logMessage("📦 \(mode)模式: 使用内置 Bundle: \(bundledURL)")
                return bundledURL
            } else {
                logMessage("❌ \(mode)模式: 内置 Bundle 不可用")
                return nil
            }
        }
        
        // CodePush 启用时的版本比较逻辑
        let codePushURL = CodePushWrapper.bundleURL()
        let bundledURL = Bundle.rnModule?.mainJSBundleURL
        
        logMessage("🔍 获取到的 Bundle URLs:")
        logMessage("   • CodePush URL: \(codePushURL?.absoluteString ?? "nil")")
        logMessage("   • 内置 Bundle URL: \(bundledURL?.absoluteString ?? "nil")")
        
        // 情况1：两个 bundle 都存在，检查是否为同一个文件
        if let codePushURL = codePushURL, let bundledURL = bundledURL {
            // 检查是否指向同一个文件（CodePush 没有真正的更新时会返回内置 bundle）
            if codePushURL.path == bundledURL.path {
                logMessage("ℹ️ \(mode)模式: CodePush 和内置 Bundle 指向同一文件，直接使用内置 Bundle")
                logMessage("📦 \(mode)模式: 使用内置 Bundle: \(bundledURL)")
                return bundledURL
            }
            
            // 不同文件才进行版本比较
            return compareBundlesAndSelect(
                codePushURL: codePushURL,
                bundledURL: bundledURL,
                mode: mode
            )
        }
        
        // 情况2：只有 CodePush bundle 存在
        if let codePushURL = codePushURL {
            logMessage("✅ \(mode)模式: 只有 CodePush 可用，使用 CodePush URL: \(codePushURL)")
            return codePushURL
        }
        
        // 情况3：只有内置 bundle 存在
        if let bundledURL = bundledURL {
            logMessage("📦 \(mode)模式: 只有内置 Bundle 可用，使用内置 Bundle: \(bundledURL)")
            return bundledURL
        }
        
        // 情况4：都没有
        logMessage("❌ \(mode)模式: 没有可用的 Bundle")
        logMessage("💥 所有 Bundle 源都失败了！")
        return nil
    }
    
    /// 比较两个 Bundle 并选择最佳的
    private func compareBundlesAndSelect(
        codePushURL: URL,
        bundledURL: URL,
        mode: String
    ) -> URL? {
        // 🔍 详细的 CodePush 信息调试
        logMessage("🔍 CodePush URL: \(codePushURL)")
        logMessage("🔍 CodePush 文件是否存在: \(FileManager.default.fileExists(atPath: codePushURL.path))")
        
        // 获取 CodePush 文件的详细信息
        if FileManager.default.fileExists(atPath: codePushURL.path) {
            do {
                let attributes = try FileManager.default.attributesOfItem(atPath: codePushURL.path)
                let fileSize = attributes[.size] as? Int64 ?? 0
                let modificationDate = attributes[.modificationDate] as? Date
                logMessage("🔍 CodePush 文件大小: \(fileSize) bytes")
                logMessage("🔍 CodePush 文件修改时间: \(modificationDate?.description ?? "未知")")
            } catch {
                logMessage("❌ 获取 CodePush 文件属性失败: \(error)")
            }
        }
        
        // 尝试读取 CodePush 包信息
        if let codePushDir = codePushURL.deletingLastPathComponent().path.components(separatedBy: "/").last {
            logMessage("🔍 CodePush 目录名: \(codePushDir)")
        }
        
        // 获取版本信息
        let codePushVersion = getBundleVersion(from: codePushURL)
        let bundledVersion = getBundleVersion(from: bundledURL)
        
        logMessage("🔍 \(mode)版本比较 - CodePush: \(codePushVersion ?? "未知") vs 内置: \(bundledVersion ?? "未知")")
        
        // 🛡️ 版本降级保护检查
        if let cpVersion = codePushVersion, let bundleVersion = bundledVersion {
            if isVersionDowngrade(from: bundleVersion, to: cpVersion) {
                logMessage("🚨 警告: 检测到版本降级 (\(bundleVersion) → \(cpVersion))，强制使用内置版本！")
                logMessage("🛡️ 版本降级保护: 使用内置 Bundle: \(bundledURL)")
                return bundledURL
            }
        }
        
        // 如果能获取到两个版本，进行比较
        if let cpVersion = codePushVersion, let bundleVersion = bundledVersion {
            let comparison = compareVersions(cpVersion, bundleVersion)
            if comparison > 0 {
                // 只有当 CodePush 版本明确更高时才选择 CodePush
                logMessage("✅ \(mode)模式成功: CodePush 版本更高(\(cpVersion) > \(bundleVersion))，使用 CodePush URL: \(codePushURL)")
                return codePushURL
            } else if comparison == 0 {
                // 版本相等时，优先使用内置版本（更安全）
                logMessage("ℹ️ \(mode)模式: 版本相等(\(cpVersion) = \(bundleVersion))，优先使用内置 Bundle: \(bundledURL)")
                return bundledURL
            } else {
                // CodePush 版本更低，使用内置版本
                logMessage("✅ \(mode)模式成功: 内置 Bundle 版本更高(\(bundleVersion) > \(cpVersion))，使用内置 Bundle: \(bundledURL)")
                return bundledURL
            }
        } else if bundledVersion != nil {
            // 如果只能获取到内置版本，优先使用内置版本（更安全）
            logMessage("⚠️ \(mode)模式: 无法获取 CodePush 版本，使用内置 Bundle: \(bundledURL)")
            return bundledURL
        } else if codePushVersion != nil {
            // 如果只能获取到 CodePush 版本
            logMessage("⚠️ \(mode)模式: 无法获取内置版本，使用 CodePush URL: \(codePushURL)")
            return codePushURL
        } else {
            // 都无法获取版本信息，回退到内置版本（更安全）
            logMessage("⚠️ \(mode)模式: 无法获取任何版本信息，回退到内置 Bundle（安全优先）: \(bundledURL)")
            return bundledURL
        }
    }
    
    // 保留原方法用于兼容性（现在只是调用新方法）
    func getPrimaryURL() -> URL? {
        return getBundleURLWithPriority()
    }
    
    // MARK: - 版本管理辅助方法
    
    /// 从 Bundle URL 获取版本信息
    private func getBundleVersion(from bundleURL: URL) -> String? {
        logMessage("🔍 尝试从 Bundle 获取版本信息: \(bundleURL)")
        
        // 方法1: 尝试读取 bundle 同目录下的 version.json
        if let versionFromSidecar = getVersionFromSidecarFile(bundleURL: bundleURL) {
            logMessage("✅ 从 sidecar 文件获取版本: \(versionFromSidecar)")
            return versionFromSidecar
        }
        
        // 方法2: 尝试从 bundle 文件名解析版本信息
        if let versionFromFilename = getVersionFromFilename(bundleURL: bundleURL) {
            logMessage("✅ 从文件名获取版本: \(versionFromFilename)")
            return versionFromFilename
        }
        
        // 方法3: 尝试从 bundle 所在的目录结构推断版本
        if let versionFromPath = getVersionFromPath(bundleURL: bundleURL) {
            logMessage("✅ 从路径推断版本: \(versionFromPath)")
            return versionFromPath
        }
        
        // 方法4: 如果是内置 Bundle，尝试从项目根目录的 version.json 获取
        let bundlePath = bundleURL.path
        if bundlePath.contains("RNModule.framework") || bundlePath.contains("main.jsbundle") {
            if let projectVersion = getVersionFromProjectRoot() {
                logMessage("✅ 从项目根目录获取内置 Bundle 版本: \(projectVersion)")
                return projectVersion
            }
        }
        
        logMessage("⚠️ 无法获取 Bundle 版本信息")
        return nil
    }
    
    /// 从 bundle 同目录的 version.json 文件获取版本
    private func getVersionFromSidecarFile(bundleURL: URL) -> String? {
        let bundleDir = bundleURL.deletingLastPathComponent()
        let versionFileURL = bundleDir.appendingPathComponent("version.json")
        
        logMessage("🔍 检查版本文件: \(versionFileURL.path)")
        
        guard FileManager.default.fileExists(atPath: versionFileURL.path) else {
            logMessage("⚠️ 版本文件不存在: \(versionFileURL.path)")
            
            // 列出目录内容以便调试
            do {
                let dirContents = try FileManager.default.contentsOfDirectory(atPath: bundleDir.path)
                logMessage("📁 目录内容: \(dirContents)")
            } catch {
                logMessage("❌ 无法读取目录内容: \(error)")
            }
            
            return nil
        }
        
        do {
            let data = try Data(contentsOf: versionFileURL)
            if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any] {
                logMessage("📋 版本文件内容: \(json)")
                if let bundleVersion = json["bundleVersion"] as? String {
                    if isValidVersionFormat(bundleVersion) {
                        logMessage("✅ 从版本文件获取版本: \(bundleVersion)")
                        return bundleVersion
                    } else {
                        logMessage("❌ 版本文件中版本格式无效: \(bundleVersion)")
                    }
                } else {
                    logMessage("⚠️ 版本文件中缺少 bundleVersion 字段")
                }
            } else {
                logMessage("❌ 版本文件格式不正确")
            }
        } catch {
            logMessage("❌ 读取版本文件失败: \(error)")
        }
        
        return nil
    }
    
    /// 从文件名解析版本信息 (例如: app-1.0.0+abc123.jsbundle)
    private func getVersionFromFilename(bundleURL: URL) -> String? {
        let filename = bundleURL.lastPathComponent
        logMessage("🔍 尝试从文件名解析版本: \(filename)")
        
        // 匹配模式: xxx-版本号+哈希.jsbundle
        let pattern = #"-(\d+\.\d+\.\d+(?:\+[a-f0-9]+)?)\."#
        
        do {
            let regex = try NSRegularExpression(pattern: pattern)
            let range = NSRange(location: 0, length: filename.count)
            
            if let match = regex.firstMatch(in: filename, range: range),
               let versionRange = Range(match.range(at: 1), in: filename) {
                let version = String(filename[versionRange])
                if isValidVersionFormat(version) {
                    logMessage("✅ 从文件名解析到版本: \(version)")
                    return version
                } else {
                    logMessage("❌ 从文件名解析的版本格式无效: \(version)")
                }
            } else {
                logMessage("⚠️ 文件名不匹配版本模式，期望格式: xxx-1.0.0+hash.jsbundle")
            }
        } catch {
            logMessage("❌ 正则表达式解析失败: \(error)")
        }
        
        return nil
    }
    
    /// 从路径推断版本信息
    private func getVersionFromPath(bundleURL: URL) -> String? {
        let pathComponents = bundleURL.pathComponents
        logMessage("🔍 尝试从路径组件解析版本: \(pathComponents)")
        
        // 查找路径中的版本号模式
        for component in pathComponents.reversed() {
            if component.range(of: #"\d+\.\d+\.\d+"#, options: .regularExpression) != nil {
                if isValidVersionFormat(component) {
                    logMessage("✅ 从路径组件找到版本: \(component)")
                    return component
                } else {
                    logMessage("⚠️ 路径组件版本格式无效: \(component)")
                }
            }
        }
        
        logMessage("⚠️ 路径中没有找到有效版本模式")
        return nil
    }
    
    /// 尝试从项目根目录的 version.json 获取内置 Bundle 版本（作为最后的备用方案）
    private func getVersionFromProjectRoot() -> String? {
        logMessage("🔍 尝试从项目根目录 version.json 获取版本...")
        
        // 构建可能的 version.json 路径
        let bundlePath = Bundle.main.bundlePath
        let possiblePaths = [
            // 直接相对于当前 bundle 的路径
            bundlePath + "/version.json",
            bundlePath + "/../version.json",
            bundlePath + "/../../version.json",
            bundlePath + "/../../../version.json",
            bundlePath + "/../../../../version.json",
            bundlePath + "/../../../../../version.json",
            bundlePath + "/../../../../../../version.json",
            // 特定的已知项目路径（开发环境）
            NSHomeDirectory() + "/Develop/Src/Weee/react-native/rn-project/version.json",
            // 当前工作目录
            FileManager.default.currentDirectoryPath + "/version.json",
            // Bundle.main.resourcePath 相关路径
            (Bundle.main.resourcePath ?? "") + "/version.json",
            (Bundle.main.resourcePath ?? "") + "/../version.json",
            (Bundle.main.resourcePath ?? "") + "/../../version.json"
        ]
        
        for path in possiblePaths {
            let normalizedPath = URL(fileURLWithPath: path).standardized.path
            logMessage("🔍 检查项目版本文件: \(normalizedPath)")
            
            if FileManager.default.fileExists(atPath: normalizedPath) {
                do {
                    let data = try Data(contentsOf: URL(fileURLWithPath: normalizedPath))
                    if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
                       let bundleVersion = json["bundleVersion"] as? String {
                        // 验证版本格式
                        if isValidVersionFormat(bundleVersion) {
                            logMessage("✅ 从项目根目录获取版本: \(bundleVersion)")
                            return bundleVersion
                        } else {
                            logMessage("❌ 项目根目录版本格式无效: \(bundleVersion)")
                        }
                    }
                } catch {
                    logMessage("❌ 读取项目版本文件失败: \(error)")
                }
            }
        }
        
        logMessage("⚠️ 项目根目录中未找到 version.json")
        return nil
    }
    
    /// 比较两个语义版本号
    /// - Returns: 1 如果 version1 > version2, -1 如果 version1 < version2, 0 如果相等
    private func compareVersions(_ version1: String, _ version2: String) -> Int {
        logMessage("🔍 比较版本: \(version1) vs \(version2)")
        
        let v1Components = parseVersion(version1)
        let v2Components = parseVersion(version2)
        
        // 比较主版本号
        if v1Components.major != v2Components.major {
            return v1Components.major > v2Components.major ? 1 : -1
        }
        
        // 比较次版本号
        if v1Components.minor != v2Components.minor {
            return v1Components.minor > v2Components.minor ? 1 : -1
        }
        
        // 比较修订版本号
        if v1Components.patch != v2Components.patch {
            return v1Components.patch > v2Components.patch ? 1 : -1
        }
        
        // 比较构建哈希（如果存在）
        return compareBuildHashes(v1Components.buildHash, v2Components.buildHash)
    }
    
    /// 解析版本字符串
    private func parseVersion(_ version: String) -> (major: Int, minor: Int, patch: Int, buildHash: String?) {
        // 分离版本号和构建哈希 (例如: "1.0.0+abc123")
        let parts = version.split(separator: "+")
        let versionPart = String(parts[0])
        let buildHash = parts.count > 1 ? String(parts[1]) : nil
        
        // 解析版本号
        let versionComponents = versionPart.split(separator: ".").compactMap { Int($0) }
        
        let major = versionComponents.count > 0 ? versionComponents[0] : 0
        let minor = versionComponents.count > 1 ? versionComponents[1] : 0
        let patch = versionComponents.count > 2 ? versionComponents[2] : 0
        
        return (major: major, minor: minor, patch: patch, buildHash: buildHash)
    }
    
    /// 比较构建哈希
    private func compareBuildHashes(_ hash1: String?, _ hash2: String?) -> Int {
        switch (hash1, hash2) {
        case (nil, nil):
            return 0
        case (nil, _):
            return -1  // 无哈希的版本较旧
        case (_, nil):
            return 1   // 有哈希的版本较新
        case (let h1?, let h2?):
            // 按字母顺序比较哈希（虽然不是严格的时间顺序，但提供一致性）
            return h1.compare(h2).rawValue
        }
    }
    
    /// 检查是否为版本降级（安全保护）
    private func isVersionDowngrade(from currentVersion: String, to newVersion: String) -> Bool {
        let comparison = compareVersions(currentVersion, newVersion)
        if comparison > 0 {
            // 当前版本 > 新版本，这是降级
            logMessage("🚨 版本降级检测: \(currentVersion) > \(newVersion)")
            return true
        }
        return false
    }
    
    /// 验证版本号是否合理（基本格式检查）
    private func isValidVersionFormat(_ version: String) -> Bool {
        // 检查是否符合语义版本格式 x.y.z 或 x.y.z+hash
        let pattern = #"^\d+\.\d+\.\d+(\+[a-f0-9]+)?$"#
        let range = version.range(of: pattern, options: .regularExpression)
        let isValid = range != nil
        
        if !isValid {
            logMessage("⚠️ 版本格式验证失败: \(version)")
        }
        
        return isValid
    }
    


    // MARK: - Bridge 实例管理方法
    
    /// 清理所有 React Native 实例
    @objc public static func cleanup() {
        logMessage("🧹 React Native 清理完成（无需操作）")
        // 注意：不再使用共享窗口架构，每个视图控制器都是独立的，无需清理
    }
    


    // MARK: - 公共 API 方法

    /// 创建 React Native 视图控制器
    /// 每次调用都创建全新的独立视图控制器，避免视图控制器层级冲突
    /// 
    /// 当 enableCodePush = false 时：
    ///   - 完全忽略 CodePush，直接使用内置 Bundle
    /// 
    /// 当 enableCodePush = true 时：
    ///   - DEBUG模式：1. metro 本地服务器 -> 2. 基于版本比较选择 codepush 或内置 bundle
    ///   - Release模式：基于版本比较选择 codepush 或内置 bundle
    @objc public static func createReactNativeViewController(
        moduleName: String, 
        initialProperties: [String: Any]?, 
        ipAddress: String? = nil,
        enableCodePush: Bool = false
    ) -> UIViewController {
        
        logMessage("🚀 创建独立的 React Native 视图控制器...")
        
        // 配置 CodePush Bundle 位置
        if enableCodePush {
            logMessage("🎯 配置 CodePush Bundle 位置...")
            CodePushWrapper.configureBundleResourceBundle()
        } else {
            logMessage("🚫 CodePush 已禁用，跳过 CodePush 配置")
        }
        
        // 设置 Metro IP 地址
        _currentMetroIP = ipAddress
        
        logMessage("🎯 Mode: \(enableCodePush ? "CodePush enabled" : "CodePush disabled")")
        if let ip = ipAddress {
            logMessage("🌐 Metro IP 已设置: \(ip)")
        } else {
            logMessage("ℹ️ Metro IP 未设置，将跳过 Metro 服务器检查")
        }
        
        // 额外的安全检查
        if ipAddress?.isEmpty == true {
            logMessage("⚠️ Metro IP 为空字符串，重置为 nil")
            _currentMetroIP = nil
        }
      
        // 1. 创建独立的 Factory（确保 Nitro 模块正确初始化）
        let factory = createReactNativeFactory(enableCodePush: enableCodePush)
        
        // 2. 使用 Factory 的 rootViewFactory 创建视图（新架构推荐方式）
        logMessage("🆕 创建独立的 React Native 视图（新架构）...")
        let rootView = factory.rootViewFactory.view(
            withModuleName: moduleName,
            initialProperties: initialProperties
        )
        
        // 3. 创建包装视图控制器并保持对 Factory 的强引用
        let reactViewController = ReactNativeViewController()
        reactViewController.factory = factory  // 保持强引用避免内存问题
        reactViewController.view = rootView
        
        // 设置初始属性（如果有的话）
        if let properties = initialProperties {
            logMessage("ℹ️ 初始属性: \(properties.count) 个属性")
        }
        
        // 调试信息
        logMessage("🔍 独立视图控制器调试信息:")
        logMessage("🔍   视图控制器类型: \(type(of: reactViewController))")
        logMessage("🔍   React Native 视图类型: \(type(of: rootView))")
        logMessage("🔍   Factory 类型: \(type(of: factory))")
        logMessage("🔍   模块名: \(moduleName)")
        
        logMessage("✅ 独立 React Native 视图控制器创建成功（新架构 + Nitro）")
        return reactViewController
    }

}

// MARK: - React Native 视图控制器
/// 自定义视图控制器，保持对 Factory 的强引用以避免内存问题
private class ReactNativeViewController: UIViewController {
    // 保持对 Factory 的强引用，防止过早释放导致 crash
    var factory: RCTReactNativeFactory?
    
    override func viewDidLoad() {
        super.viewDidLoad()
        logMessage("🔧 ReactNativeViewController viewDidLoad")
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        logMessage("🔧 ReactNativeViewController viewWillAppear")
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        logMessage("🔧 ReactNativeViewController viewDidAppear")
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        logMessage("🔧 ReactNativeViewController viewWillDisappear")
    }
    
    override func viewDidDisappear(_ animated: Bool) {
        super.viewDidDisappear(animated)
        logMessage("🔧 ReactNativeViewController viewDidDisappear")
    }
    
    deinit {
        logMessage("🗑️ ReactNativeViewController 被释放")
        factory = nil
    }
}

// MARK: - Bundle 资源管理扩展
extension Bundle {
    static var rnModule: Bundle? {
        logMessage("🔍 开始搜索 RNModule Bundle...")
        
        // 输出环境信息
        let mainBundle = Bundle.main
        logMessage("🌍 主 Bundle 信息:")
        logMessage("🌍   路径: \(mainBundle.bundlePath)")
        logMessage("🌍   标识符: \(mainBundle.bundleIdentifier ?? "无")")
        logMessage("🌍   资源路径: \(mainBundle.resourcePath ?? "无")")
        
        // 检查运行环境
        #if targetEnvironment(simulator)
        logMessage("🌍 运行环境: 模拟器")
        #else
        logMessage("🌍 运行环境: 真机")
        #endif
        
        // 统计所有可用 Bundle
        let allBundles = Bundle.allBundles + Bundle.allFrameworks
        logMessage("📊 系统 Bundle 统计: 总共 \(allBundles.count) 个")
        logMessage("📊   allBundles: \(Bundle.allBundles.count) 个")
        logMessage("📊   allFrameworks: \(Bundle.allFrameworks.count) 个")
        
        // 1️⃣ 优先：尝试访问 Swift Package 中的 Bundle（最终集成方式）
        logMessage("🎯 步骤 1/3: 搜索 Swift Package Bundle...")
        if let swiftPackageBundle = getSwiftPackageBundle() {
            logMessage("✅ ✨ 成功找到 Swift Package 中的 RNModule Bundle")
            return swiftPackageBundle
        }
        logMessage("❌ 步骤 1 失败: 未找到 Swift Package Bundle")
        
        // 2️⃣ 回退：尝试访问 XCFramework 中的 Bundle（当前测试）
        logMessage("🎯 步骤 2/3: 搜索 XCFramework Bundle...")
        if let xcframeworkBundle = getXCFrameworkBundle() {
            logMessage("✅ ✨ 成功找到 XCFramework 中的 RNModule Bundle")
            return xcframeworkBundle
        }
        logMessage("❌ 步骤 2 失败: 未找到 XCFramework Bundle")
        
        // 3️⃣ 回退：检查传统 Framework 和主 Bundle
        logMessage("🎯 步骤 3/3: 搜索传统方式...")
        
        // 尝试传统 framework 路径
        if let frameworkPath = Bundle.main.path(forResource: "RNModule", ofType: "framework"),
           let bundle = Bundle(path: frameworkPath) {
            logMessage("✅ ✨ 找到传统 Framework: \(frameworkPath)")
            return bundle
        }
        
        // 最后回退：检查主 Bundle
        if Bundle.main.url(forResource: "main", withExtension: "jsbundle") != nil {
            logMessage("✅ ✨ 使用主 Bundle 作为最终回退")
            return Bundle.main
        }
        
        logMessage("❌ 步骤 3 失败: 传统方式未找到")
        
        logMessage("💥 🚨 所有查找方式都失败了！🚨")
        return nil
    }
    
    /// 获取 Swift Package 中的 Bundle（最终集成方式）
    private static func getSwiftPackageBundle() -> Bundle? {
        logMessage("🔍 查找 Swift Package Bundle...")
        
        // Swift Package 的 Bundle 访问方式（按成功率排序）
        let possibleIdentifiers = [
            // 1. 已验证成功的 Bundle 标识符（优先）
            "com.sayweee.www.RNModule",
            // 2. 模块 Bundle（Swift 5.3+ 支持 Bundle.module）
            "RNModule",
            // 3. GitHub 组织名的标识符格式
            "io.github.sayweee.RNModule"
        ]
        
        for identifier in possibleIdentifiers {
            logMessage("🔍 尝试 Bundle 标识符: \(identifier)")
            
            if let bundle = Bundle(identifier: identifier) {
                logMessage("✅ 找到 Bundle: \(identifier) at \(bundle.bundlePath)")
                
                // 验证是否包含 main.jsbundle
                if let jsBundleURL = bundle.url(forResource: "main", withExtension: "jsbundle") {
                    logMessage("✅ Swift Package Bundle 包含 main.jsbundle: \(jsBundleURL)")
                    return bundle
                } else {
                    logMessage("⚠️ Swift Package Bundle 存在但缺少 main.jsbundle")
                    // 列出 Bundle 中的所有资源文件进行调试
                    if let resourcePath = bundle.resourcePath {
                        let resourceURL = URL(fileURLWithPath: resourcePath)
                        do {
                            let contents = try FileManager.default.contentsOfDirectory(at: resourceURL, includingPropertiesForKeys: nil)
                            logMessage("📋 Bundle 内容: \(contents.map { $0.lastPathComponent })")
                        } catch {
                            logMessage("❌ 无法读取 Bundle 内容: \(error)")
                        }
                    }
                }
            }
        }
        
        // 扫描所有已加载的 Bundle（简化版）
        logMessage("🔍 扫描已加载的相关 Bundle...")
        let allBundles = Bundle.allBundles + Bundle.allFrameworks
        
        for bundle in allBundles {
            if let bundleId = bundle.bundleIdentifier,
               bundleId.contains("RNModule"),
               let jsBundleURL = bundle.url(forResource: "main", withExtension: "jsbundle") {
                logMessage("✅ 找到相关 Bundle: \(bundleId) -> \(jsBundleURL)")
                return bundle
            }
        }
        
        logMessage("⚠️ 未找到 Swift Package Bundle")
        return nil
    }
    
    /// 获取 XCFramework 中的 Bundle（简化版）
    private static func getXCFrameworkBundle() -> Bundle? {
        logMessage("🔍 搜索 XCFramework Bundle...")
        
        // 简化：只检查最常用的两个路径
        let mainBundlePath = Bundle.main.bundlePath
        let possiblePaths = [
            "\(mainBundlePath)/../../../xcframeworks/RNModule.xcframework",
            "\(mainBundlePath)/../../xcframeworks/RNModule.xcframework"
        ]
        
        for basePath in possiblePaths {
            let normalizedPath = URL(fileURLWithPath: basePath).standardized.path
            logMessage("🔍 检查 XCFramework 路径: \(normalizedPath)")
            
            if FileManager.default.fileExists(atPath: normalizedPath),
               let bundle = tryGetXCFrameworkBundle(at: normalizedPath) {
                logMessage("✅ 找到 XCFramework Bundle: \(normalizedPath)")
                return bundle
            }
        }
        
        logMessage("⚠️ 未找到 XCFramework Bundle")
        return nil
    }
    
    /// 尝试从指定 XCFramework 路径获取 Bundle（简化版）
    private static func tryGetXCFrameworkBundle(at xcframeworkPath: String) -> Bundle? {
        logMessage("🔍 检查 XCFramework: \(xcframeworkPath)")
        
        // 根据运行环境优先选择对应架构
        let architecturePaths = [
            "\(xcframeworkPath)/ios-arm64_x86_64-simulator/RNModule.framework",  // 模拟器
            "\(xcframeworkPath)/ios-arm64/RNModule.framework"                    // 真机
        ]
        
        for frameworkPath in architecturePaths {
            if FileManager.default.fileExists(atPath: frameworkPath),
               let bundle = Bundle(path: frameworkPath),
               let jsBundleURL = bundle.url(forResource: "main", withExtension: "jsbundle") {
                logMessage("✅ 找到 XCFramework Bundle: \(frameworkPath)")
                logMessage("✅ 包含 main.jsbundle: \(jsBundleURL.path)")
                return bundle
            }
        }
        
        logMessage("❌ XCFramework 中未找到有效 Bundle")
        return nil
    }
    
    var mainJSBundleURL: URL? {
        logMessage("🔍 开始在 Bundle 中搜索 main.jsbundle...")
        logMessage("🔍 当前 Bundle 路径: \(bundlePath)")
        
        // 优先在当前 bundle 中查找
        if let bundleURL = url(forResource: "main", withExtension: "jsbundle") {
            logMessage("✅ 在当前 Bundle 中找到 main.jsbundle: \(bundleURL)")
            return bundleURL
        }
        
        // 备用：在主 Bundle 中查找
        if let mainURL = Bundle.main.url(forResource: "main", withExtension: "jsbundle") {
            logMessage("✅ 在主 Bundle 中找到 main.jsbundle: \(mainURL)")
            return mainURL
        }
        
        logMessage("❌ 未找到 main.jsbundle")
        return nil
    }
}

