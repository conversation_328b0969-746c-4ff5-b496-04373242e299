// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		A11396AF2E72A31C007A3FEB /* RNModuleSwiftPackage in Frameworks */ = {isa = PBXBuildFile; productRef = A11396AE2E72A31C007A3FEB /* RNModuleSwiftPackage */; };
		A19045A02E5ED62C004FF739 /* WeeeCore in Frameworks */ = {isa = PBXBuildFile; productRef = A190459F2E5ED62C004FF739 /* WeeeCore */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		A15B8FB92E30E2CB005EDF9B /* KakukuSPM.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = KakukuSPM.app; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		A15B8FCB2E30E2CC005EDF9B /* Exceptions for "KakukuSPM" folder in "KakukuSPM" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = A15B8FB82E30E2CB005EDF9B /* KakukuSPM */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		A15B8FBB2E30E2CB005EDF9B /* KakukuSPM */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				A15B8FCB2E30E2CC005EDF9B /* Exceptions for "KakukuSPM" folder in "KakukuSPM" target */,
			);
			path = KakukuSPM;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		A15B8FB62E30E2CB005EDF9B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A19045A02E5ED62C004FF739 /* WeeeCore in Frameworks */,
				A11396AF2E72A31C007A3FEB /* RNModuleSwiftPackage in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		A15B8FB02E30E2CB005EDF9B = {
			isa = PBXGroup;
			children = (
				A15B8FBB2E30E2CB005EDF9B /* KakukuSPM */,
				A190459E2E5ED62C004FF739 /* Frameworks */,
				A15B8FBA2E30E2CB005EDF9B /* Products */,
			);
			sourceTree = "<group>";
		};
		A15B8FBA2E30E2CB005EDF9B /* Products */ = {
			isa = PBXGroup;
			children = (
				A15B8FB92E30E2CB005EDF9B /* KakukuSPM.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		A190459E2E5ED62C004FF739 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		A15B8FB82E30E2CB005EDF9B /* KakukuSPM */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A15B8FCC2E30E2CC005EDF9B /* Build configuration list for PBXNativeTarget "KakukuSPM" */;
			buildPhases = (
				A15B8FB52E30E2CB005EDF9B /* Sources */,
				A15B8FB62E30E2CB005EDF9B /* Frameworks */,
				A15B8FB72E30E2CB005EDF9B /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				A15B8FBB2E30E2CB005EDF9B /* KakukuSPM */,
			);
			name = KakukuSPM;
			packageProductDependencies = (
				A190459F2E5ED62C004FF739 /* WeeeCore */,
				A11396AE2E72A31C007A3FEB /* RNModuleSwiftPackage */,
			);
			productName = KakukuSPM;
			productReference = A15B8FB92E30E2CB005EDF9B /* KakukuSPM.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		A15B8FB12E30E2CB005EDF9B /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1620;
				LastUpgradeCheck = 1620;
				TargetAttributes = {
					A15B8FB82E30E2CB005EDF9B = {
						CreatedOnToolsVersion = 16.2;
					};
				};
			};
			buildConfigurationList = A15B8FB42E30E2CB005EDF9B /* Build configuration list for PBXProject "KakukuSPM" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = A15B8FB02E30E2CB005EDF9B;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				A12EE3042E54288D007F9C52 /* XCRemoteSwiftPackageReference "mobile-core-ios" */,
				A11396AD2E72A31C007A3FEB /* XCLocalSwiftPackageReference "../../../../rn-project" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = A15B8FBA2E30E2CB005EDF9B /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				A15B8FB82E30E2CB005EDF9B /* KakukuSPM */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		A15B8FB72E30E2CB005EDF9B /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		A15B8FB52E30E2CB005EDF9B /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		A15B8FCD2E30E2CC005EDF9B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_CXX_LANGUAGE_STANDARD = "$(inherited)";
				CLANG_CXX_LIBRARY = "libc++";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 65CX6ZDWYZ;
				GCC_PREPROCESSOR_DEFINITIONS = "$(inherited)";
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = KakukuSPM/Info.plist;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				OTHER_CPLUSPLUSFLAGS = (
					"$(inherited)",
					"-DRCT_NEW_ARCH_ENABLED=1",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.andy.www.KakukuSPM;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		A15B8FCE2E30E2CC005EDF9B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_CXX_LANGUAGE_STANDARD = "$(inherited)";
				CLANG_CXX_LIBRARY = "libc++";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 65CX6ZDWYZ;
				GCC_PREPROCESSOR_DEFINITIONS = "$(inherited)";
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = KakukuSPM/Info.plist;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				OTHER_CPLUSPLUSFLAGS = (
					"$(inherited)",
					"-DRCT_NEW_ARCH_ENABLED=1",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.andy.www.KakukuSPM;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		A15B8FCF2E30E2CC005EDF9B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "$(inherited)";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CPLUSPLUSFLAGS = (
					"$(inherited)",
					"-DRCT_NEW_ARCH_ENABLED=1",
				);
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		A15B8FD02E30E2CC005EDF9B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "$(inherited)";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = "$(inherited)";
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				OTHER_CPLUSPLUSFLAGS = (
					"$(inherited)",
					"-DRCT_NEW_ARCH_ENABLED=1",
				);
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		A15B8FB42E30E2CB005EDF9B /* Build configuration list for PBXProject "KakukuSPM" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A15B8FCF2E30E2CC005EDF9B /* Debug */,
				A15B8FD02E30E2CC005EDF9B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A15B8FCC2E30E2CC005EDF9B /* Build configuration list for PBXNativeTarget "KakukuSPM" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A15B8FCD2E30E2CC005EDF9B /* Debug */,
				A15B8FCE2E30E2CC005EDF9B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCLocalSwiftPackageReference section */
		A11396AD2E72A31C007A3FEB /* XCLocalSwiftPackageReference "../../../../rn-project" */ = {
			isa = XCLocalSwiftPackageReference;
			relativePath = "../../../../rn-project";
		};
/* End XCLocalSwiftPackageReference section */

/* Begin XCRemoteSwiftPackageReference section */
		A12EE3042E54288D007F9C52 /* XCRemoteSwiftPackageReference "mobile-core-ios" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/sayweee/mobile-core-ios.git";
			requirement = {
				kind = upToNextMinorVersion;
				minimumVersion = 6.9.46;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		A11396AE2E72A31C007A3FEB /* RNModuleSwiftPackage */ = {
			isa = XCSwiftPackageProductDependency;
			productName = RNModuleSwiftPackage;
		};
		A190459F2E5ED62C004FF739 /* WeeeCore */ = {
			isa = XCSwiftPackageProductDependency;
			package = A12EE3042E54288D007F9C52 /* XCRemoteSwiftPackageReference "mobile-core-ios" */;
			productName = WeeeCore;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = A15B8FB12E30E2CB005EDF9B /* Project object */;
}
