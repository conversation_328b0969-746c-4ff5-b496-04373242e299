package com.margelo.nitro.weee;

import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.facebook.react.uimanager.ThemedReactContext;

import org.jetbrains.annotations.NotNull;

public class WeeeAtcButton extends HybridWeeeAtcButtonSpec {

    ThemedReactContext context;

    public WeeeAtcButton(ThemedReactContext context) {
        this.context = context;
    }

    @Override
    public long getQty() {
        return 0;
    }

    @Override
    public void setQty(long l) {
        if (getView() instanceof TextView) {
            ((TextView) getView()).setText("abc");
        }
    }

    @Override
    public @NotNull View getView() {
//        View opLayout = ViewFactoryManager.Companion.getINSTANCE().getFactory().getView(context);
//        opLayout.setLayoutParams(new ViewGroup.LayoutParams(200,
//                100));
//        return opLayout;
        TextView tv = new TextView(context);
        tv.setLayoutParams(new ViewGroup.LayoutParams(100, 200));
        return tv;
    }
}