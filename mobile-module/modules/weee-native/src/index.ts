import type { TestResult } from './types/TestResult';

import { getHostComponent } from 'react-native-nitro-modules'
import { AtcButtonMethods, AtcButtonProps } from './nitro/WeeeAtcButton.nitro';
import WeeeAtcButtonConfig from '../nitrogen/generated/shared/json/WeeeAtcButtonConfig.json'

// ==================== 类型导出 ====================

import Toast from './utils/toast';
import Cache from './utils/cache';
import Network from './utils/network';
import Sync from './utils/sync';
import Route from './utils/route';
import { WeeeResponse } from './types/WeeeResponse';

export type {
  WeeeResponse,
  TestResult 
}

export { 
  Network, 
  Toast, 
  Cache, 
  Route, 
  Sync, 
}

export const WeeeAtcButton2 = getHostComponent<AtcButtonProps, AtcButtonMethods>(
  '',
  () => WeeeAtcButtonConfig
)