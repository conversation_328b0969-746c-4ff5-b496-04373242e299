import Foundation
import NitroModules
import UIKit

// WeeeToast 实现类 - 继承生成的 HybridWeeeToastSpec  
class WeeeToast: HybridWeeeToastSpec {
    
    // MARK: - MobileCore Bridge 集成
    
    /// MobileCore Bridge 集成 - 与其他模块保持一致的测试架构
    private let bridge = WeeeNativeBridge.shared
    
    // Toast 显示历史
    private var toastHistory: [String] = []
    
    // MARK: - Toast 功能实现
    
    func show(message: String) throws -> Void {
        print("📢 [WeeeToast] 显示 Toast: \(message)")
        
        // 记录到历史
        toastHistory.append(message)
        
        // 在主线程显示 Toast（实际项目中可以集成真正的 Toast 组件）
        DispatchQueue.main.async {
            // 这里可以集成实际的 Toast 显示逻辑
            // 比如使用 UIAlertController 或自定义 Toast View
            print("🎬 [WeeeToast] Toast 显示在屏幕上: \(message)")
        }
    }
    
    // MARK: - 测试功能实现
    
    func testBasicFunction() throws -> TestResult {
        print("🧪 [WeeeToast] Toast 模块基础功能测试，集成 MobileCore")
        
        // 先测试模块自身功能
        try show(message: "测试消息1")
        try show(message: "测试消息2")
        
        // 调用 MobileCore 进行集成测试
        let mobileCoreResult = bridge.callMobileCore(OperationType.toast.rawValue)
        
        // 解析 MobileCore JSON 响应
        var success = false
        var message = "Toast test completed"
        
        do {
            if let data = mobileCoreResult.data(using: .utf8),
               let resultDict = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any] {
                success = resultDict["result"] as? Bool ?? false
                message = resultDict["message"] as? String ?? "Unknown response"
            }
        } catch {
            print("⚠️ [WeeeToast] Failed to parse MobileCore response: \(error.localizedDescription)")
        }
        
        // 组合测试数据
        let testData = "Toast test - toasts shown: \(toastHistory.count), last: \(toastHistory.last ?? "none"), MobileCore: \(success ? "✅" : "❌")"
        
        print("✅ [WeeeToast] Toast 模块测试完成，MobileCore 集成: \(success)")
        
        return TestResult(
            success: success,
            message: "\(message) [Toast模块]",
            data: testData
        )
    }
    
    // MARK: - 初始化
    
    public override init() {
        super.init()
        print("🚀 [WeeeToast] WeeeToast initialized")
    }
}
