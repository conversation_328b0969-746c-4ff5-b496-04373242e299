import Foundation
import NitroModules

// WeeeCache 实现类 - 继承生成的 HybridWeeeCacheSpec
class WeeeCache: HybridWeeeCacheSpec {
    
    // MARK: - MobileCore Bridge 集成
    
    /// MobileCore Bridge 集成 - 与其他模块保持一致的测试架构
    private let bridge = WeeeNativeBridge.shared
    
    // MARK: - 缓存功能实现
    
    func zipcode() throws -> String {
        print("🏠 [WeeeCache] 获取邮编信息")
        // 模拟返回邮编数据
        return "94041"
    }
    
    func deliveryDate() throws -> String {
        print("📅 [WeeeCache] 获取配送日期")
        // 模拟返回配送日期
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        return formatter.string(from: Calendar.current.date(byAdding: .day, value: 1, to: Date()) ?? Date())
    }
    
    func simpleOrderItem(productId: String, productKey: String) throws -> String {
        print("🛒 [WeeeCache] 创建简单订单项")
        print("   - 产品ID: \(productId)")
        print("   - 产品Key: \(productKey)")
        
        // 模拟返回订单项数据
        let orderItem: [String: Any] = [
            "productId": productId,
            "productKey": productKey,
            "quantity": 1,
            "timestamp": Date().timeIntervalSince1970
        ]
        
        do {
            let jsonData = try JSONSerialization.data(withJSONObject: orderItem, options: [])
            return String(data: jsonData, encoding: .utf8) ?? "{}"
        } catch {
            print("⚠️ [WeeeCache] JSON serialization failed: \(error.localizedDescription)")
            return "{}"
        }
    }
    
    func language() throws -> String {
        print("🌐 [WeeeCache] 获取语言设置")
        // 返回当前系统语言
        return Locale.current.languageCode ?? "en"
    }
    
    // MARK: - 测试功能实现
    
    func testBasicFunction() throws -> TestResult {
        print("🧪 [WeeeCache] Cache 模块基础功能测试，集成 MobileCore")
        
        // 先测试模块自身功能
        let currentZipcode = try zipcode()
        let currentDeliveryDate = try deliveryDate()
        
        // 调用 MobileCore 进行集成测试
        let mobileCoreResult = bridge.callMobileCore(OperationType.cache.rawValue)
        
        // 解析 MobileCore JSON 响应
        var success = false
        var message = "Cache test completed"
        
        do {
            if let data = mobileCoreResult.data(using: .utf8),
               let resultDict = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any] {
                success = resultDict["result"] as? Bool ?? false
                message = resultDict["message"] as? String ?? "Unknown response"
            }
        } catch {
            print("⚠️ [WeeeCache] Failed to parse MobileCore response: \(error.localizedDescription)")
        }
        
        // 组合测试数据
        let testData = "Cache test - zipcode: \(currentZipcode), deliveryDate: \(currentDeliveryDate), MobileCore: \(success ? "✅" : "❌")"
        
        print("✅ [WeeeCache] Cache 模块测试完成，MobileCore 集成: \(success)")
        
        // 将解析后的数据转换为 TestResult
        return TestResult(
            success: success,
            message: "\(message) [Cache模块]",
            data: testData
        )
    }
    
    // MARK: - 初始化
    
    public override init() {
        super.init()
        print("🚀 [WeeeCache] WeeeCache initialized")
    }
}
