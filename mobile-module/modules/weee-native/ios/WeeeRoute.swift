import Foundation
import NitroModules

// WeeeRoute 实现类 - 继承生成的 HybridWeeeRouteSpec
class WeeeRoute: HybridWeeeRouteSpec {
    
    // MARK: - MobileCore Bridge 集成
    
    /// MobileCore Bridge 集成 - 与其他模块保持一致的测试架构
    private let bridge = WeeeNativeBridge.shared
    
    // 路由栈管理
    private var routeStack: [String] = []
    
    // MARK: - 路由功能实现
    
    func push(url: String) throws -> Void {
        print("🎯 [WeeeRoute] 推送新路由: \(url)")
        routeStack.append(url)
        print("📍 [WeeeRoute] 当前路由栈深度: \(routeStack.count)")
    }
    
    func pop() throws -> Void {
        print("🔙 [WeeeRoute] 回退路由")
        if !routeStack.isEmpty {
            let removedUrl = routeStack.removeLast()
            print("🗑️ [WeeeRoute] 移除路由: \(removedUrl)")
        } else {
            print("⚠️ [WeeeRoute] 路由栈为空，无法回退")
        }
        print("📍 [WeeeRoute] 当前路由栈深度: \(routeStack.count)")
    }
    
    // MARK: - 测试功能实现
    
    func testBasicFunction() throws -> TestResult {
        print("🧪 [WeeeRoute] Route 模块基础功能测试，集成 MobileCore")
        
        // 先测试模块自身功能
        try push(url: "/test/home")
        try push(url: "/test/profile")
        try pop()
        
        // 调用 MobileCore 进行集成测试
        let mobileCoreResult = bridge.callMobileCore(OperationType.route.rawValue)
        
        // 解析 MobileCore JSON 响应
        var success = false
        var message = "Route test completed"
        
        do {
            if let data = mobileCoreResult.data(using: .utf8),
               let resultDict = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any] {
                success = resultDict["result"] as? Bool ?? false
                message = resultDict["message"] as? String ?? "Unknown response"
            }
        } catch {
            print("⚠️ [WeeeRoute] Failed to parse MobileCore response: \(error.localizedDescription)")
        }
        
        // 组合测试数据
        let testData = "Route test - stack size: \(routeStack.count), operations: push/push/pop, MobileCore: \(success ? "✅" : "❌")"
        
        print("✅ [WeeeRoute] Route 模块测试完成，MobileCore 集成: \(success)")
        
        // 将解析后的数据转换为 TestResult
        return TestResult(
            success: success,
            message: "\(message) [Route模块]",
            data: testData
        )
    }
    
    // MARK: - 初始化
    
    public override init() {
        super.init()
        print("🚀 [WeeeRoute] WeeeRoute initialized")
    }
}
