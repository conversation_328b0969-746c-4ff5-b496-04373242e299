import Foundation
import NitroModules

// 导入生成的 Swift 类型（这些文件会被自动包含在模块中）

// Nitro 实现类 - 继承生成的 HybridWeeeNetworkSpec
class WeeeNetwork: HybridWeeeNetworkSpec {
    
    // MARK: - 桥接集成
    
    /// MobileCore Bridge 集成 - 使用简单的桥接实例（现在使用全局提供者作为回退）
    private let bridge = WeeeNativeBridge.shared
    
    // MARK: - 辅助方法
    
    /// 将 JSON 字符串转换为 [String: Any] - 通用 object 支持
    private func parseJsonParams(_ jsonString: String?) -> [String: Any]? {
        guard let jsonString = jsonString, !jsonString.isEmpty else { return nil }
        
        guard let data = jsonString.data(using: .utf8) else {
            print("❌ [WeeeNetwork] Invalid JSON string encoding")
            return nil
        }
        
        do {
            let jsonObject = try JSONSerialization.jsonObject(with: data, options: [])
            if let dictionary = jsonObject as? [String: Any] {
                return dictionary
            } else {
                print("⚠️ [WeeeNetwork] JSON is not a dictionary object, wrapping in 'data' key")
                return ["data": jsonObject]
            }
        } catch {
            print("❌ [WeeeNetwork] JSON parsing error: \(error.localizedDescription)")
            return nil
        }
    }
    
    /// 解析批量请求的 JSON 字符串为数组
    private func parseBatchJsonParams(_ jsonString: String) -> [[String: Any]]? {
        guard let data = jsonString.data(using: .utf8) else {
            print("❌ [WeeeNetwork] Invalid batch JSON string encoding")
            return nil
        }
        
        do {
            let jsonObject = try JSONSerialization.jsonObject(with: data, options: [])
            if let arrayOfDictionaries = jsonObject as? [[String: Any]] {
                return arrayOfDictionaries
            } else if let singleDictionary = jsonObject as? [String: Any] {
                // 如果传入的是单个对象，包装成数组
                return [singleDictionary]
            } else {
                print("⚠️ [WeeeNetwork] Batch JSON is not an array of objects, wrapping in array")
                return [["data": jsonObject]]
            }
        } catch {
            print("❌ [WeeeNetwork] Batch JSON parsing error: \(error.localizedDescription)")
            return nil
        }
    }
    
    /// 创建 JSON 响应字符串 - 完全通用的响应格式
    private func createJsonResponse(
        result: Bool,
        message: String,
        messageId: String,
        data: [String: Any]
    ) -> String {
        let response: [String: Any] = [
            "result": result,
            "message": message,
            "messageId": messageId,
            "data": data,
            "timestamp": Date().timeIntervalSince1970
        ]
        
        do {
            let jsonData = try JSONSerialization.data(withJSONObject: response, options: [])
            return String(data: jsonData, encoding: .utf8) ?? "{\"error\": \"JSON serialization failed\"}"
        } catch {
            print("⚠️ [WeeeNetwork] JSON serialization failed: \(error.localizedDescription)")
            return "{\"result\": false, \"message\": \"JSON serialization failed\", \"messageId\": \"json_error\", \"data\": {}}"
        }
    }
    
    // MARK: - 网络方法实现 (集成 MobileCore Bridge)
    
    // MARK: - JSON 参数方法 (通用 object 支持)
    
    func get(url: String, paramsJson: String?) throws -> String {
        print("🌐 [WeeeNetwork] GET request (JSON): \(url)")
        if let jsonStr = paramsJson {
            print("📄 [WeeeNetwork] JSON params: \(jsonStr)")
        }
        
        let parsedParams = parseJsonParams(paramsJson)
        let requestParams: [String: Any] = [
            "url": url,
            "method": "GET",
            "params": parsedParams ?? [:],
            "paramsType": "json"
        ]
        
        // 优先使用 mobile-core-ios
        let jsonResult = bridge.callMobileCore(OperationType.network.rawValue)
        
        // 解析 JSON 响应
        do {
            if let data = jsonResult.data(using: .utf8),
               let resultDict = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any],
               let success = resultDict["result"] as? Bool, success {
                print("✅ [WeeeNetwork] GET request completed via MobileCore: \(url)")
                return jsonResult
            } else {
                print("🔄 [WeeeNetwork] MobileCore unavailable for GET, using native implementation")
                return createNativeResponse(url: url, method: "GET", params: parsedParams)
            }
        } catch {
            print("🔄 [WeeeNetwork] MobileCore JSON parsing failed, using native implementation")
            return createNativeResponse(url: url, method: "GET", params: parsedParams)
        }
    }
    
    func post(url: String, paramsJson: String?) throws -> String {
        print("🌐 [WeeeNetwork] POST request (JSON): \(url)")
        if let jsonStr = paramsJson {
            print("📄 [WeeeNetwork] JSON params: \(jsonStr)")
        }
        
        let parsedParams = parseJsonParams(paramsJson)
        let requestParams: [String: Any] = [
            "url": url,
            "method": "POST",
            "params": parsedParams ?? [:],
            "paramsType": "json"
        ]
        
        let jsonResult = bridge.callMobileCore(OperationType.network.rawValue)
        
        // 解析 JSON 响应
        do {
            if let data = jsonResult.data(using: .utf8),
               let resultDict = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any],
               let success = resultDict["result"] as? Bool, success {
                print("✅ [WeeeNetwork] POST request completed via MobileCore: \(url)")
                return jsonResult
            } else {
                print("🔄 [WeeeNetwork] MobileCore unavailable for POST, using native implementation")
                return createNativeResponse(url: url, method: "POST", params: parsedParams)
            }
        } catch {
            print("🔄 [WeeeNetwork] MobileCore JSON parsing failed, using native implementation")
            return createNativeResponse(url: url, method: "POST", params: parsedParams)
        }
    }
    
    func postBatch(url: String, requestsJson: String?) throws -> String {
        print("🌐 [WeeeNetwork] POST Batch request (JSON): \(url)")
        if let jsonStr = requestsJson {
            print("📄 [WeeeNetwork] Batch JSON: \(jsonStr)")
        }
        
        let requests = requestsJson != nil ? parseBatchJsonParams(requestsJson!) : nil
        let responseData: [String: Any] = [
            "url": url,
            "method": "POST_BATCH", 
            "requestCount": requests?.count ?? 0,
            "timestamp": Date().timeIntervalSince1970,
            "framework": "Nitro",
            "paramsType": "json"
        ]
        
        return createJsonResponse(
            result: true,
            message: "POST batch request successful",
            messageId: "post_batch_success",
            data: responseData
        )
    }
    
    func put(url: String, paramsJson: String?) throws -> String {
        print("🌐 [WeeeNetwork] PUT request (JSON): \(url)")
        
        let parsedParams = parseJsonParams(paramsJson)
        let responseData: [String: Any] = [
            "url": url,
            "method": "PUT",
            "params": parsedParams ?? [:],
            "timestamp": Date().timeIntervalSince1970,
            "framework": "Nitro",
            "paramsType": "json"
        ]
        
        return createJsonResponse(
            result: true,
            message: "PUT request successful",
            messageId: "put_success",
            data: responseData
        )
    }
    
    func putBatch(url: String, requestsJson: String?) throws -> String {
        print("🌐 [WeeeNetwork] PUT Batch request (JSON): \(url)")
        if let jsonStr = requestsJson {
            print("📄 [WeeeNetwork] Batch JSON: \(jsonStr)")
        }
        
        let requests = requestsJson != nil ? parseBatchJsonParams(requestsJson!) : nil
        let responseData: [String: Any] = [
            "url": url,
            "method": "PUT_BATCH",
            "requestCount": requests?.count ?? 0,
            "timestamp": Date().timeIntervalSince1970,
            "framework": "Nitro",
            "paramsType": "json"
        ]
        
        return createJsonResponse(
            result: true,
            message: "PUT batch request successful",
            messageId: "put_batch_success",
            data: responseData
        )
    }
    
    func del(url: String) throws -> String {
        let responseData: [String: Any] = [
            "url": url,
            "method": "DELETE",
            "timestamp": Date().timeIntervalSince1970,
            "framework": "Nitro"
        ]
        
        return createJsonResponse(
            result: true,
            message: "DELETE request successful",
            messageId: "delete_success",
            data: responseData
        )
    }
    
    func testBasicFunction() throws -> String {
        print("🧪 [WeeeNetwork] Basic function test with MobileCore Bridge")
        
        // 🔗 关键修复：调用桥接实例 - 使用枚举类型确保类型安全
        let mobileCoreJsonResult = bridge.callMobileCore(OperationType.test.rawValue)
        
        // 解析 MobileCore 的 JSON 响应
        var mobileMessage = "MobileCore test completed"
        var mobileMessageId = "test_success"
        var mobileCoreData: [String: Any] = [:]
        
        do {
            if let data = mobileCoreJsonResult.data(using: .utf8),
               let resultDict = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any] {
                mobileMessage = resultDict["message"] as? String ?? "Unknown response"
                mobileMessageId = resultDict["messageId"] as? String ?? "unknown"
                mobileCoreData = resultDict
            }
        } catch {
            print("⚠️ [WeeeNetwork] Failed to parse MobileCore response: \(error.localizedDescription)")
        }
        
        let testData: [String: Any] = [
            "framework": "Nitro + MobileCore Bridge",
            "module": "WeeeNetwork",
            "platform": "iOS",
            "version": "1.0.0",
            "timestamp": Date().timeIntervalSince1970,
            "status": "working",
            "mobile_core_data": mobileCoreData
        ]
        
        print("🎯 [WeeeNetwork] MobileCore 原始消息: \(mobileMessage)")
        print("✅ [WeeeNetwork] Basic function test completed")
        
        return createJsonResponse(
            result: true,
            message: "\(mobileMessage) [via WeeeNetwork]",
            messageId: mobileMessageId,
            data: testData
        )
    }
    
    // MARK: - 异步方法实现 (Nitro 协议要求)
    
    func getAsync(url: String, paramsJson: String?) throws -> Promise<String> {
        print("⚡ [WeeeNetwork] Async GET request (JSON): \(url)")
        
        return Promise.async {
            // 直接返回同步调用结果
            return try self.get(url: url, paramsJson: paramsJson)
        }
    }
    
    func postAsync(url: String, paramsJson: String?) throws -> Promise<String> {
        print("⚡ [WeeeNetwork] Async POST request (JSON): \(url)")
        
        return Promise.async {
            // 直接返回同步调用结果
            return try self.post(url: url, paramsJson: paramsJson)
        }
    }
    
    
    // MARK: - 私有辅助方法
    

    
    /// 创建 Promise 的辅助方法
    private func createPromise(_ executor: @escaping (@escaping ([String: Any]) -> Void) -> Void) -> Any {
        // 使用 Nitro 的 Promise 实现
        // 这是一个简化版本，实际应该使用 Nitro 的 Promise 类型
        return [
            "then": { (callback: @escaping ([String: Any]) -> Void) in
                DispatchQueue.global().async {
                    executor { result in
                        DispatchQueue.main.async {
                            callback(result)
                        }
                    }
                }
            }
        ]
    }
    
    // MARK: - Nitro 模块注册
    // 根据官方文档要求，必须提供默认构造函数用于 autolinking
    
    // MARK: - 原生回退实现
    
    /// 创建原生回退响应
    private func createNativeResponse(url: String, method: String, params: [String: Any]?) -> String {
        print("🔧 [WeeeNetwork] Creating native fallback response for \(method): \(url)")
        
        let responseData: [String: Any] = [
            "url": url,
            "method": method,
            "params": params ?? [:],
            "timestamp": Date().timeIntervalSince1970,
            "framework": "Nitro Native Fallback",
            "note": "mobile-core-ios not available, using native implementation"
        ]
        
        return createJsonResponse(
            result: true,
            message: "\(method) request successful (native fallback)",
            messageId: "\(method.lowercased())_native_success",
            data: responseData
        )
    }
    
    // MARK: - Nitro 模块注册
    
    public override init() {
        super.init()
        print("🚀 [WeeeNetwork] WeeeNetwork initialized with MobileCore Bridge support")
    }
}
